<template>
  <div class="profile-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <el-button :icon="ArrowLeft" @click="$router.push('/home')" class="back-button">
          返回首页
        </el-button>
        <h1>个人中心</h1>
        <div class="user-info-preview">
          <span class="welcome-text">欢迎回来，{{ userInfo?.username || '用户' }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <main class="main-content">
      <div class="profile-card">
        <!-- 头像和基本信息 -->
        <div class="profile-header">
          <div class="avatar-section">
            <div class="avatar-wrapper" @click="handleAvatarClick">
              <el-avatar :src="userInfo?.avatarUrl" :size="140" class="user-avatar">
                {{ userInfo?.username?.charAt(0) }}
              </el-avatar>
              <div class="avatar-overlay">
                <el-icon :size="28">
                  <Camera />
                </el-icon>
                <span>更换头像</span>
              </div>
            </div>
            <input
              ref="avatarInputRef"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleAvatarChange"
            />
            <h2 class="username">{{ userInfo?.username || '未设置' }}</h2>
            <p class="user-id">{{ userInfo?.account || '账号未设置' }}</p>
          </div>

          <div class="stats-section">
            <div class="stat-item">
              <p class="stat-value">{{ formatDate(userInfo?.registerTime) }}</p>
              <p class="stat-label">注册时间</p>
            </div>
            <div class="stat-item">
              <p class="stat-value">{{ formatDate(userInfo?.lastLoginTime) }}</p>
              <p class="stat-label">最后登录</p>
            </div>
          </div>
        </div>

        <!-- 用户信息表单 -->
        <div class="form-section">
          <div class="section-title">
            <h3>详细信息</h3>
          </div>
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="120px"
            class="profile-form"
            size="large"
          >
            <div class="form-grid">
              <el-form-item label="用户名" prop="username" class="form-item">
                <el-input
                  v-model="profileForm.username"
                  placeholder="请输入用户名"
                  :disabled="!isEditing"
                  :class="isEditing ? 'edit-input' : ''"
                />
              </el-form-item>

              <el-form-item label="手机号" prop="phone" class="form-item">
                <el-input
                  v-model="profileForm.phone"
                  placeholder="请输入手机号"
                  :disabled="!isEditing"
                  :class="isEditing ? 'edit-input' : ''"
                />
              </el-form-item>

              <el-form-item label="邮箱" prop="email" class="form-item">
                <el-input
                  v-model="profileForm.email"
                  placeholder="请输入邮箱"
                  :disabled="!isEditing"
                  :class="isEditing ? 'edit-input' : ''"
                />
              </el-form-item>

              <el-form-item label="地址" prop="address" class="form-item">
                <el-input
                  v-model="profileForm.address"
                  placeholder="请输入地址"
                  :disabled="!isEditing"
                  :class="isEditing ? 'edit-input' : ''"
                />
              </el-form-item>
            </div>

            <el-form-item class="action-buttons">
              <template v-if="!isEditing">
                <el-button
                  type="primary"
                  @click="startEdit"
                  class="edit-btn"
                  :size="'large'"
                >
                  编辑信息
                </el-button>
              </template>
              <template v-else>
                <el-button
                  type="primary"
                  :loading="saving"
                  @click="saveProfile"
                  class="save-btn"
                  :size="'large'"
                >
                  保存
                </el-button>
                <el-button @click="cancelEdit" class="cancel-btn" :size="'large'">
                  取消
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Camera } from '@element-plus/icons-vue'
import { getUserInfo, updateUser, uploadAvatar } from '@/api/user'

const profileFormRef = ref()
const avatarInputRef = ref()
const userInfo = ref(null)
const isEditing = ref(false)
const saving = ref(false)
const originalForm = ref({})

// 表单数据
const profileForm = reactive({
  username: '',
  account: '',
  phone: '',
  email: '',
  address: ''
})

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2-20个字符之间', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  address: [
    { max: 100, message: '地址长度不能超过100个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadUserInfo()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      const user = JSON.parse(storedUserInfo)
      userInfo.value = user
      
      // 从服务器获取最新信息
      const response = await getUserInfo(user.id)
      userInfo.value = response.data
      
      // 更新表单数据
      updateFormData()
      
      // 更新本地存储
      localStorage.setItem('userInfo', JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
    ElMessage.error('加载用户信息失败，请刷新页面重试')
  }
}

// 更新表单数据
const updateFormData = () => {
  profileForm.username = userInfo.value?.username || ''
  profileForm.account = userInfo.value?.account || ''
  profileForm.phone = userInfo.value?.phone || ''
  profileForm.email = userInfo.value?.email || ''
  profileForm.address = userInfo.value?.address || ''
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  originalForm.value = { ...profileForm }
  // 添加编辑状态动画类
  document.querySelector('.profile-card')?.classList.add('editing')
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  Object.assign(profileForm, originalForm.value)
  // 移除编辑状态动画类
  document.querySelector('.profile-card')?.classList.remove('editing')
}

// 保存用户信息
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    saving.value = true
    
    const updateData = {
      username: profileForm.username,
      phone: profileForm.phone,
      email: profileForm.email,
      address: profileForm.address
    }
    
    const response = await updateUser(userInfo.value.id, updateData)
    
    userInfo.value = response.data
    localStorage.setItem('userInfo', JSON.stringify(response.data))
    
    isEditing.value = false
    document.querySelector('.profile-card')?.classList.remove('editing')
    ElMessage.success('信息更新成功')
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    saving.value = false
  }
}

// 处理头像点击
const handleAvatarClick = () => {
  avatarInputRef.value?.click()
}

// 处理头像更换
const handleAvatarChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 检查文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }
  
  try {
    // 添加加载效果
    const avatarElement = document.querySelector('.user-avatar')
    if (avatarElement) {
      avatarElement.classList.add('uploading')
    }
    
    const response = await uploadAvatar(userInfo.value.id, file)
    
    userInfo.value.avatarUrl = response.data
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    
    ElMessage.success('头像更新成功')
    
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败，请稍后重试')
  } finally {
    // 移除加载效果
    setTimeout(() => {
      const avatarElement = document.querySelector('.user-avatar')
      if (avatarElement) {
        avatarElement.classList.remove('uploading')
      }
    }, 500)
    
    // 清空文件输入
    event.target.value = ''
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>

<style scoped>
/* 全局样式 */
.profile-container {
  min-height: 100vh;
  background-color: #f7f9fc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 头部样式 */
.header {
  background: linear-gradient(90deg, #165DFF 0%, #0080FF 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.back-button {
  color: white;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  color: white;
}

.user-info-preview {
  display: flex;
  align-items: center;
}

.welcome-text {
  font-size: 14px;
  opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 32px 24px;
}

/* 卡片样式 */
.profile-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.05), 0 3px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.profile-card.editing {
  box-shadow: 0 10px 24px rgba(22, 93, 255, 0.15);
}

/* 个人信息头部 */
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #f0f2f5;
}

/* 头像区域 */
.avatar-section {
  text-align: center;
  margin-bottom: 24px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin-bottom: 16px;
}

.user-avatar {
  border: 4px solid #f0f2f5;
  transition: all 0.3s ease;
}

.user-avatar.uploading {
  animation: pulse 1.5s infinite;
  opacity: 0.7;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(22, 93, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(22, 93, 255, 0);
  }
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: all 0.3s ease;
  font-size: 14px;
  backdrop-filter: blur(2px);
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.username {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1d2129;
}

.user-id {
  font-size: 14px;
  color: #86909c;
  margin: 0;
}

/* 统计信息 */
.stats-section {
  display: flex;
  justify-content: center;
  gap: 48px;
  width: 100%;
  margin-top: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin: 0 0 4px 0;
}

.stat-label {
  font-size: 14px;
  color: #86909c;
  margin: 0;
}

/* 表单区域 */
.section-title {
  margin-bottom: 24px;
}

.section-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin: 0;
  padding-left: 12px;
  border-left: 4px solid #165DFF;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.profile-form .form-item {
  margin-bottom: 0;
}

.edit-input {
  border-color: #165DFF;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

/* 按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
}

.edit-btn {
  background: #165DFF;
  border-color: #165DFF;
  padding: 10px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background: #0E4FDB;
  border-color: #0E4FDB;
}

.save-btn {
  background: #00B42A;
  border-color: #00B42A;
  padding: 10px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: #00A327;
  border-color: #00A327;
}

.cancel-btn {
  background: #F2F3F5;
  color: #4E5969;
  border-color: #F2F3F5;
  padding: 10px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: #E5E6EB;
  border-color: #E5E6EB;
}
</style>
