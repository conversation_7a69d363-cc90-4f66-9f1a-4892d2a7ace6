<template>
  <div class="home-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <h1 class="logo">角色扮演网站</h1>
        <div class="user-info">
          <el-avatar :src="userInfo?.avatarUrl" :size="40">
            {{ userInfo?.username?.charAt(0) }}
          </el-avatar>
          <span class="username">{{ userInfo?.username }}</span>
          <el-dropdown @command="handleCommand">
            <el-button type="text" :icon="ArrowDown" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 横向导航栏 -->
    <div class="nav-bar">
      <div class="nav-content">
        <div class="custom-nav-menu">
          <div
            class="nav-item"
            :class="{ active: activeTab === 'home' }"
            @click="handleTabSelect('home')"
          >
            <el-icon><House /></el-icon>
            <span>首页</span>
          </div>
          <div
            class="nav-item"
            :class="{ active: activeTab === 'roleplay' }"
            @click="handleTabSelect('roleplay')"
          >
            <el-icon><ChatDotRound /></el-icon>
            <span>角色扮演</span>
          </div>
          <div
            class="nav-item"
            :class="{ active: activeTab === 'profile' }"
            @click="handleTabSelect('profile')"
          >
            <el-icon><User /></el-icon>
            <span>个人信息</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 首页内容 -->
      <div v-if="activeTab === 'home'" class="tab-content">
        <HomeContent />
      </div>

      <!-- 角色扮演内容 -->
      <div v-else-if="activeTab === 'roleplay'" class="tab-content">
        <RoleplayContent />
      </div>

      <!-- 个人信息内容 -->
      <div v-else-if="activeTab === 'profile'" class="tab-content">
        <ProfileContent />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, ChatDotRound, User, House, Tools } from '@element-plus/icons-vue'
import ProfileContent from '../components/ProfileContent.vue'
import RoleplayContent from '../components/RoleplayContent.vue'
import HomeContent from '../components/HomeContent.vue'

const router = useRouter()
const userInfo = ref(null)
const activeTab = ref('home')

onMounted(() => {
  // 获取用户信息
  const storedUserInfo = localStorage.getItem('userInfo')
  if (storedUserInfo) {
    userInfo.value = JSON.parse(storedUserInfo)
  }

  // 监听标签页切换事件
  window.addEventListener('switchTab', handleSwitchTab)
})

// 处理标签页切换
const handleSwitchTab = (event) => {
  activeTab.value = event.detail
}

// 处理导航栏选择
const handleTabSelect = (key) => {
  activeTab.value = key
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    localStorage.removeItem('userInfo')
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消
  }
}


</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  color: #409EFF;
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.username {
  font-weight: 500;
  color: #333;
}

/* 导航栏样式 */
.nav-bar {
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-nav-menu {
  display: flex;
  gap: 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  color: #606266;
  border-bottom: 3px solid transparent;
  white-space: nowrap;
  min-width: 120px;
  justify-content: center;
}

.nav-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.nav-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background: #ecf5ff;
}

.nav-item .el-icon {
  font-size: 16px;
}

.nav-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 内容区域样式 */
.content-area {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.tab-content {
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

/* 欢迎区域样式 */
.welcome-section {
  text-align: center;
}

.welcome-section h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 28px;
}

.welcome-section p {
  color: #666;
  font-size: 16px;
  margin-bottom: 10px;
}

.welcome-section .sub-text {
  color: #999;
  font-size: 14px;
}

/* 开发中提示样式 */
.developing-notice {
  text-align: center;
  padding: 60px 20px;
}

.developing-notice h2 {
  color: #333;
  margin: 20px 0 15px;
  font-size: 24px;
}

.developing-notice p {
  color: #666;
  font-size: 16px;
  margin-bottom: 20px;
}
</style>
