<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
        <div class="character-info">
          <el-avatar :src="character?.avatarUrl" :size="40">
            {{ character?.name?.charAt(0) }}
          </el-avatar>
          <div class="character-details">
            <h3>{{ character?.name }}</h3>
            <span class="character-category">{{ character?.category }}</span>
          </div>
        </div>
      </div>

      <div class="header-right">
        <!-- 好感度显示 -->
        <div class="favor-display">
          <span class="favor-label">好感度</span>
          <el-progress
            :percentage="favorPercentage"
            :stroke-width="8"
            :show-text="false"
            color="#f56c6c"
            style="width: 100px;"
          />
          <span class="favor-value">{{ favorValue }}</span>
        </div>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message-item', message.type]"
      >
        <!-- AI消息（左边） -->
        <div v-if="message.type === 'ai'" class="message-wrapper">
          <el-avatar :src="character?.avatarUrl" :size="40">
            {{ character?.name?.charAt(0) }}
          </el-avatar>
          <div class="message-content">
            <div class="message-bubble ai-message">
              {{ message.content }}
            </div>
            <!-- AI内心独白 -->
            <div v-if="message.innerThought" class="inner-thought">
              {{ message.innerThought }}
            </div>
          </div>
        </div>

        <!-- 用户消息（右边） -->
        <div v-else class="message-wrapper user-wrapper">
          <div class="message-content">
            <div class="message-bubble user-message">
              {{ message.content }}
            </div>
          </div>
          <el-avatar :src="userInfo?.avatarUrl" :size="40">
            {{ userInfo?.username?.charAt(0) }}
          </el-avatar>
        </div>
      </div>

      <!-- 加载中提示 -->
      <div v-if="isTyping" class="message-item ai">
        <div class="message-wrapper">
          <el-avatar :src="character?.avatarUrl" :size="40">
            {{ character?.name?.charAt(0) }}
          </el-avatar>
          <div class="message-content">
            <div class="message-bubble ai-message typing">
              <span class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </span>
              {{ character?.name }} 正在思考中...（可能需要1-2分钟）
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="2"
          placeholder="输入消息..."
          @keydown.enter.exact="handleSend"
          @keydown.enter.shift.exact.prevent="inputMessage += '\n'"
          :disabled="isTyping"
        />
        <el-button
          type="primary"
          :loading="isTyping"
          @click="handleSend"
          :disabled="!inputMessage.trim()"
        >
          发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getCharacterById } from '@/api/character'
import { sendMessage, getChatHistory, getFavorValue } from '@/api/chat'

const router = useRouter()
const route = useRoute()

const messagesContainer = ref()
const character = ref(null)
const userInfo = ref(null)
const messages = ref([])
const inputMessage = ref('')
const isTyping = ref(false)
const favorValue = ref(0)
const favorPercentage = ref(0)

// 路由参数
const userId = ref(route.query.userId)
const characterId = ref(route.query.characterId)

onMounted(() => {
  initChat()
})

// 监听好感度变化
watch(favorValue, (newValue) => {
  favorPercentage.value = Math.min(newValue, 100)
})

// 初始化聊天
const initChat = async () => {
  try {
    // 获取用户信息
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      userInfo.value = JSON.parse(storedUserInfo)
    }

    // 获取角色信息
    const characterRes = await getCharacterById(characterId.value)
    character.value = characterRes.data

    // 获取聊天历史
    await loadChatHistory()

    // 获取好感度
    await loadFavorValue()

  } catch (error) {
    console.error('初始化聊天失败:', error)
    ElMessage.error('初始化聊天失败')
  }
}

// 加载聊天历史
const loadChatHistory = async () => {
  try {
    const response = await getChatHistory(userId.value, characterId.value)
    const records = response.data || []

    // 转换聊天记录格式
    const messageMap = new Map()

    records.forEach(record => {
      if (record.messageType === 1) { // 用户消息
        messageMap.set(record.id, {
          id: record.id,
          type: 'user',
          content: record.content,
          timestamp: record.chatTime
        })
      } else if (record.messageType === 2) { // AI回复
        messageMap.set(record.id, {
          id: record.id,
          type: 'ai',
          content: record.content,
          timestamp: record.chatTime
        })
      } else if (record.messageType === 3) { // AI内心独白
        // 找到对应的AI回复消息
        const aiMessage = Array.from(messageMap.values()).find(msg =>
          msg.type === 'ai' && Math.abs(new Date(msg.timestamp) - new Date(record.chatTime)) < 1000
        )
        if (aiMessage) {
          aiMessage.innerThought = record.content
        }
      }
    })

    messages.value = Array.from(messageMap.values()).sort((a, b) =>
      new Date(a.timestamp) - new Date(b.timestamp)
    )

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })

  } catch (error) {
    console.error('加载聊天历史失败:', error)
  }
}

// 加载好感度
const loadFavorValue = async () => {
  try {
    const response = await getFavorValue(userId.value, characterId.value)
    favorValue.value = response.data || 0
  } catch (error) {
    console.error('加载好感度失败:', error)
  }
}

// 发送消息
const handleSend = async () => {
  if (!inputMessage.value.trim() || isTyping.value) return

  const message = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加用户消息到界面
  const userMessage = {
    id: Date.now(),
    type: 'user',
    content: message,
    timestamp: new Date().toISOString()
  }
  messages.value.push(userMessage)

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })

  // 显示AI正在输入
  isTyping.value = true

  try {
    // 发送消息到后端
    const response = await sendMessage({
      userId: parseInt(userId.value),
      characterId: parseInt(characterId.value),
      message: message
    })

    // 添加AI回复到界面
    const aiMessage = {
      id: Date.now() + 1,
      type: 'ai',
      content: response.data.reply,
      innerThought: response.data.innerThought,
      timestamp: new Date().toISOString()
    }
    messages.value.push(aiMessage)

    // 更新好感度
    favorValue.value = response.data.favorValue || favorValue.value

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })

  } catch (error) {
    console.error('发送消息失败:', error)

    let errorMessage = '发送消息失败'
    if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接或稍后重试'
    } else if (error.response) {
      errorMessage = `服务器错误: ${error.response.status}`
    } else if (error.request) {
      errorMessage = '网络连接失败，请检查后端服务是否启动'
    }

    ElMessage.error(errorMessage)
  } finally {
    isTyping.value = false
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 返回
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.chat-header {
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.character-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.character-details h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.character-category {
  font-size: 12px;
  color: #909399;
}

.favor-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.favor-label {
  font-size: 14px;
  color: #666;
}

.favor-value {
  font-size: 14px;
  color: #f56c6c;
  font-weight: bold;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.message-item {
  margin-bottom: 20px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-wrapper {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  line-height: 1.4;
}

.ai-message {
  background: white;
  color: #333;
  border: 1px solid #e4e7ed;
}

.user-message {
  background: #409EFF;
  color: white;
}

.inner-thought {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 12px;
  font-size: 13px;
  color: #666;
  font-style: italic;
}

.typing {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #409EFF;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.chat-input {
  background: white;
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-wrapper .el-textarea {
  flex: 1;
}
</style>
