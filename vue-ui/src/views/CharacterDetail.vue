<template>
  <div class="character-detail-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-content">
        <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
        <h1>角色详情</h1>
        <div></div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content" v-loading="loading">
      <div class="character-detail-card" v-if="character">
        <!-- 头像部分 -->
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <el-avatar :src="character.avatarUrl" :size="120">
              {{ character.name?.charAt(0) }}
            </el-avatar>
            <div class="avatar-overlay" @click="handleAvatarClick">
              <el-icon :size="24">
                <Camera />
              </el-icon>
              <span>更换头像</span>
            </div>
          </div>
          <input
            ref="avatarInputRef"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleAvatarChange"
          />
        </div>

        <!-- 角色信息表单 -->
        <div class="form-section">
          <el-form
            ref="characterFormRef"
            :model="characterForm"
            :rules="characterRules"
            label-width="100px"
            class="character-form"
          >
            <el-form-item label="角色名称" prop="name">
              <el-input
                v-model="characterForm.name"
                placeholder="请输入角色名称"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="角色分类" prop="category">
              <el-select
                v-model="characterForm.category"
                placeholder="请选择角色分类"
                :disabled="!isEditing"
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="角色描述" prop="description">
              <el-input
                v-model="characterForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入角色描述"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="角色人设" prop="personality">
              <el-input
                v-model="characterForm.personality"
                type="textarea"
                :rows="6"
                placeholder="请输入角色人设"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="背景图URL" prop="backgroundUrl">
              <el-input
                v-model="characterForm.backgroundUrl"
                placeholder="请输入背景图URL（可选）"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="排序权重" prop="sortOrder">
              <el-input-number
                v-model="characterForm.sortOrder"
                :min="1"
                :max="100"
                :disabled="!isEditing"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="是否启用" prop="isActive">
              <el-switch
                v-model="characterForm.isActive"
                :disabled="!isEditing"
              />
            </el-form-item>

            <el-form-item label="创建时间">
              <el-input
                :value="formatDate(character.createdAt)"
                disabled
              />
            </el-form-item>

            <el-form-item label="更新时间">
              <el-input
                :value="formatDate(character.updatedAt)"
                disabled
              />
            </el-form-item>

            <el-form-item>
              <el-button
                v-if="!isEditing"
                type="primary"
                @click="startEdit"
              >
                编辑信息
              </el-button>
              <template v-else>
                <el-button
                  type="primary"
                  :loading="saving"
                  @click="saveCharacter"
                >
                  保存
                </el-button>
                <el-button @click="cancelEdit">
                  取消
                </el-button>
              </template>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Camera } from '@element-plus/icons-vue'
import { getCharacterById, getAllCategories, updateCharacter } from '@/api/character'
import { uploadAvatar } from '@/api/user'

const router = useRouter()
const route = useRoute()
const characterFormRef = ref()
const avatarInputRef = ref()
const character = ref(null)
const loading = ref(false)
const isEditing = ref(false)
const saving = ref(false)
const originalForm = ref({})
const categories = ref([])

// 角色ID
const characterId = ref(route.params.id)

// 表单数据
const characterForm = reactive({
  name: '',
  category: '',
  description: '',
  personality: '',
  avatarUrl: '',
  backgroundUrl: '',
  sortOrder: 1,
  isActive: true
})

// 表单验证规则
const characterRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择角色分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ],
  personality: [
    { required: true, message: '请输入角色人设', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    
    // 并行加载角色详情和分类
    const [characterRes, categoriesRes] = await Promise.all([
      getCharacterById(characterId.value),
      getAllCategories()
    ])
    
    character.value = characterRes.data
    categories.value = categoriesRes.data || []
    
    // 更新表单数据
    updateFormData()
    
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 更新表单数据
const updateFormData = () => {
  characterForm.name = character.value?.name || ''
  characterForm.category = character.value?.category || ''
  characterForm.description = character.value?.description || ''
  characterForm.personality = character.value?.personality || ''
  characterForm.avatarUrl = character.value?.avatarUrl || ''
  characterForm.backgroundUrl = character.value?.backgroundUrl || ''
  characterForm.sortOrder = character.value?.sortOrder || 1
  characterForm.isActive = character.value?.isActive || true
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  originalForm.value = { ...characterForm }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  Object.assign(characterForm, originalForm.value)
}

// 保存角色信息
const saveCharacter = async () => {
  if (!characterFormRef.value) return
  
  try {
    await characterFormRef.value.validate()
    saving.value = true
    
    const updateData = {
      name: characterForm.name,
      category: characterForm.category,
      description: characterForm.description,
      personality: characterForm.personality,
      avatarUrl: characterForm.avatarUrl,
      backgroundUrl: characterForm.backgroundUrl,
      sortOrder: characterForm.sortOrder,
      isActive: characterForm.isActive
    }
    
    const response = await updateCharacter(characterId.value, updateData)
    
    character.value = response.data
    isEditing.value = false
    ElMessage.success('角色信息更新成功')
    
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 处理头像点击
const handleAvatarClick = () => {
  if (!isEditing.value) {
    ElMessage.warning('请先点击编辑按钮')
    return
  }
  avatarInputRef.value?.click()
}

// 处理头像更换
const handleAvatarChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 检查文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }
  
  try {
    // 使用用户头像上传接口（复用OSS上传功能）
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const response = await uploadAvatar(userInfo.id, file)
    
    // 更新表单中的头像URL
    characterForm.avatarUrl = response.data
    character.value.avatarUrl = response.data
    
    ElMessage.success('头像上传成功')
    
  } catch (error) {
    console.error('头像上传失败:', error)
    ElMessage.error('头像上传失败')
  }
  
  // 清空文件输入
  event.target.value = ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 返回
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.character-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
}

.character-detail-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-section {
  text-align: center;
  margin-bottom: 40px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.form-section {
  max-width: 600px;
  margin: 0 auto;
}

.character-form .el-form-item {
  margin-bottom: 24px;
}
</style>
