<template>
  <div class="roleplay-content">
    <!-- 分类筛选 -->
    <div class="category-filter">
      <el-button
        :type="selectedCategory === '' ? 'primary' : ''"
        @click="selectCategory('')"
      >
        全部
      </el-button>
      <el-button
        v-for="category in categories"
        :key="category"
        :type="selectedCategory === category ? 'primary' : ''"
        @click="selectCategory(category)"
      >
        {{ category }}
      </el-button>
    </div>

    <!-- 角色列表 -->
    <div class="character-grid" v-loading="loading">
      <div
        v-for="character in paginatedCharacters"
        :key="character.id"
        class="character-card"
        :style="{ backgroundImage: `url(${character.backgroundUrl || character.avatarUrl})` }"
      >
        <!-- 卡片遮罩层 -->
        <div class="card-overlay">
          <!-- 角色头像（小尺寸） -->
          <div class="character-avatar-small">
            <el-avatar :src="character.avatarUrl" :size="50">
              {{ character.name.charAt(0) }}
            </el-avatar>
          </div>

          <div class="character-info">
            <h3 class="character-name">{{ character.name }}</h3>
            <p class="character-category">{{ character.category }}</p>
            <p class="character-description">{{ character.description }}</p>

            <!-- 好感度进度条 -->
            <div class="favor-section" v-if="character.favorValue !== undefined">
              <div class="favor-label">
                <span>好感度</span>
                <span>{{ character.favorValue || 0 }}</span>
              </div>
              <el-progress
                :percentage="character.favorPercentage || 0"
                :stroke-width="6"
                :show-text="false"
                color="#f56c6c"
              />
            </div>
          </div>

          <div class="character-actions">
            <el-button type="primary" size="small" @click.stop="startChat(character)">
              开始聊天
            </el-button>
            <el-button type="info" size="small" @click.stop="viewDetail(character)">
              查看详情
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div v-if="!loading && filteredCharacters.length > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="filteredCharacters.length"
        layout="prev, pager, next, jumper, total"
        :hide-on-single-page="false"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && filteredCharacters.length === 0" class="empty-state">
      <el-empty description="暂无角色数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCharactersByUserId, getAllCategories } from '@/api/character'

const router = useRouter()
const loading = ref(false)
const characters = ref([])
const categories = ref([])
const selectedCategory = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(6) // 每页显示6个角色

// 过滤后的角色列表
const filteredCharacters = computed(() => {
  if (!selectedCategory.value) {
    return characters.value
  }
  return characters.value.filter(char => char.category === selectedCategory.value)
})

// 分页后的角色列表
const paginatedCharacters = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCharacters.value.slice(start, end)
})

onMounted(() => {
  loadData()
})

// 加载数据
const loadData = async () => {
  try {
    loading.value = true

    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.id) {
      ElMessage.error('请先登录')
      return
    }

    // 并行加载角色列表和分类
    const [charactersRes, categoriesRes] = await Promise.all([
      getCharactersByUserId(userInfo.id),
      getAllCategories()
    ])

    characters.value = charactersRes.data || []
    categories.value = categoriesRes.data || []

  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 选择分类
const selectCategory = (category) => {
  selectedCategory.value = category
  currentPage.value = 1 // 切换分类时重置到第一页
}

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page
}

// 开始聊天
const startChat = (character) => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (!userInfo.id) {
    ElMessage.error('请先登录')
    return
  }

  // 跳转到聊天页面
  router.push({
    path: '/chat',
    query: {
      userId: userInfo.id,
      characterId: character.id,
      characterName: character.name
    }
  })
}

// 查看详情
const viewDetail = (character) => {
  router.push({
    path: `/character/${character.id}`
  })
}
</script>

<style scoped>
.roleplay-content {
  padding: 20px;
}

.category-filter {
  margin-bottom: 30px;
  text-align: center;
}

.category-filter .el-button {
  margin: 0 8px 8px 0;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.character-card {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  min-height: 280px;
}

.character-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.5) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
}

.character-avatar-small {
  text-align: center;
  margin-bottom: 10px;
}

.character-info {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.character-name {
  color: white;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.character-category {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 10px 0;
  font-size: 12px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.character-description {
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 15px 0;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.favor-section {
  margin-top: 15px;
}

.favor-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.character-actions {
  text-align: center;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding: 20px 0;
}

.pagination-container .el-pagination {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #f5f7fa;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
}

.pagination-container .el-pagination .btn-prev,
.pagination-container .el-pagination .btn-next {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.pagination-container .el-pagination .btn-prev:hover,
.pagination-container .el-pagination .btn-next:hover {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container .el-pagination .el-pager li {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.pagination-container .el-pagination .el-pager li:hover {
  color: #409eff;
  border-color: #409eff;
}

.pagination-container .el-pagination .el-pager li.is-active {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}
</style>
