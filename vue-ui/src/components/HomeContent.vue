<template>
  <div class="home-content">
    <!-- 轮播图 -->
    <div class="carousel-section">
      <el-carousel height="300px" :interval="4000" arrow="hover" indicator-position="outside">
        <el-carousel-item v-for="character in carouselCharacters" :key="character.id">
          <div class="carousel-item" @click="startChat(character)">
            <div class="carousel-bg" :style="{ backgroundImage: `url(${character.backgroundUrl || character.avatarUrl})` }">
              <div class="carousel-overlay">
                <div class="carousel-content">
                  <el-avatar :src="character.avatarUrl" :size="80">
                    {{ character.name?.charAt(0) }}
                  </el-avatar>
                  <h2>{{ character.name }}</h2>
                  <p>{{ character.description }}</p>
                  <div class="favor-info">
                    <span>好感度: {{ character.favorValue || 0 }}</span>
                    <el-progress
                      :percentage="character.favorPercentage || 0"
                      :stroke-width="6"
                      :show-text="false"
                      color="#f56c6c"
                      style="width: 200px; margin-top: 8px;"
                    />
                  </div>
                  <el-button type="primary" size="large">开始聊天</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="32" color="#409EFF">
              <ChatDotRound />
            </el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ chatStats.totalChats || 0 }}</h3>
            <p>总聊天次数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="32" color="#67C23A">
              <Calendar />
            </el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ chatStats.todayChats || 0 }}</h3>
            <p>今日聊天</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="32" color="#E6A23C">
              <User />
            </el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ chatStats.chatCharacterCount || 0 }}</h3>
            <p>聊天角色数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">
            <el-icon :size="32" color="#F56C6C">
              <Clock />
            </el-icon>
          </div>
          <div class="stat-content">
            <h3>{{ chatStats.mostActiveHour || '--' }}</h3>
            <p>最活跃时段</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 好感度排行榜 -->
    <div class="ranking-section">
      <div class="section-header">
        <h2>好感度排行榜</h2>
        <el-button type="text" @click="viewAllCharacters">查看全部</el-button>
      </div>

      <div class="ranking-chart" ref="rankingChart" style="height: 400px;"></div>
    </div>

    <!-- 最近聊天和推荐角色 -->
    <div class="bottom-section">
      <!-- 最近聊天 -->
      <div class="recent-chats">
        <div class="section-header">
          <h2>最近聊天</h2>
        </div>

        <div class="chat-list">
          <div
            v-for="chat in recentChats"
            :key="chat.id"
            class="chat-item"
            @click="continueChatWith(chat)"
          >
            <el-avatar :src="chat.character_avatar" :size="40">
              {{ chat.character_name?.charAt(0) }}
            </el-avatar>
            <div class="chat-info">
              <h4>{{ chat.character_name }}</h4>
              <p>{{ chat.content }}</p>
              <span class="chat-time">{{ formatTime(chat.chat_time) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐角色 -->
      <div class="recommended-characters">
        <div class="section-header">
          <h2>推荐角色</h2>
        </div>

        <div class="character-grid">
          <div
            v-for="character in recommendedCharacters"
            :key="character.id"
            class="mini-character-card"
            :style="{ backgroundImage: `url(${character.backgroundUrl || character.avatarUrl})` }"
            @click="startChat(character)"
          >
            <div class="mini-card-overlay">
              <el-avatar :src="character.avatarUrl" :size="40">
                {{ character.name?.charAt(0) }}
              </el-avatar>
              <h4>{{ character.name }}</h4>
              <p>{{ character.category }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Calendar, User, Clock } from '@element-plus/icons-vue'
import { getHomeData } from '@/api/home'
import * as echarts from 'echarts'

const router = useRouter()
const rankingChart = ref()

// 数据
const carouselCharacters = ref([])
const favorRanking = ref([])
const chatStats = ref({})
const recentChats = ref([])
const recommendedCharacters = ref([])

onMounted(() => {
  loadHomeData()
})

// 加载首页数据
const loadHomeData = async () => {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    if (!userInfo.id) {
      ElMessage.error('请先登录')
      return
    }

    const response = await getHomeData(userInfo.id)
    const data = response.data

    carouselCharacters.value = data.carouselCharacters || []
    favorRanking.value = data.favorRanking || []
    chatStats.value = data.chatStats || {}
    recentChats.value = data.recentChats || []
    recommendedCharacters.value = data.recommendedCharacters || []

    // 渲染图表
    nextTick(() => {
      renderRankingChart()
    })

  } catch (error) {
    console.error('加载首页数据失败:', error)
  }
}

// 渲染好感度排行榜图表
const renderRankingChart = () => {
  if (!rankingChart.value || favorRanking.value.length === 0) return

  const chart = echarts.init(rankingChart.value)

  const option = {
    title: {
      text: '角色好感度排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: favorRanking.value.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '好感度'
    },
    series: [{
      data: favorRanking.value.map(item => ({
        value: item.favorValue,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#f56c6c' },
            { offset: 1, color: '#ffa8a8' }
          ])
        }
      })),
      type: 'bar',
      barWidth: '60%'
    }]
  }

  chart.setOption(option)

  // 点击图表跳转到聊天
  chart.on('click', (params) => {
    const character = favorRanking.value[params.dataIndex]
    if (character) {
      startChat(character)
    }
  })
}

// 开始聊天
const startChat = (character) => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (!userInfo.id) {
    ElMessage.error('请先登录')
    return
  }

  router.push({
    path: '/chat',
    query: {
      userId: userInfo.id,
      characterId: character.id,
      characterName: character.name
    }
  })
}

// 继续聊天
const continueChatWith = (chat) => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (!userInfo.id) {
    ElMessage.error('请先登录')
    return
  }

  router.push({
    path: '/chat',
    query: {
      userId: userInfo.id,
      characterId: chat.character_id,
      characterName: chat.character_name
    }
  })
}

// 查看所有角色
const viewAllCharacters = () => {
  // 切换到角色扮演标签
  const event = new CustomEvent('switchTab', { detail: 'roleplay' })
  window.dispatchEvent(event)
}

// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return ''
  const time = new Date(timeString)
  const now = new Date()
  const diff = now - time

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}
</script>

<style scoped>
.home-content {
  padding: 20px;
}

/* 轮播图样式 */
.carousel-section {
  margin-bottom: 40px;
}

.carousel-item {
  height: 100%;
  cursor: pointer;
}

.carousel-bg {
  height: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.carousel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-content {
  text-align: center;
  color: white;
}

.carousel-content h2 {
  margin: 15px 0 10px;
  font-size: 24px;
}

.carousel-content p {
  margin: 0 0 20px;
  font-size: 16px;
  opacity: 0.9;
}

.favor-info {
  margin: 20px 0;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-content h3 {
  margin: 0 0 8px;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 排行榜样式 */
.ranking-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
}

/* 底部区域样式 */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.recent-chats,
.recommended-characters {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.chat-item:hover {
  background-color: #f5f5f5;
}

.chat-info {
  flex: 1;
}

.chat-info h4 {
  margin: 0 0 4px;
  font-size: 14px;
  color: #333;
}

.chat-info p {
  margin: 0 0 4px;
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-time {
  font-size: 11px;
  color: #999;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.mini-character-card {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.mini-character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.mini-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: white;
}

.mini-card-overlay h4 {
  margin: 8px 0 4px;
  font-size: 14px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.mini-card-overlay p {
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

@media (max-width: 768px) {
  .bottom-section {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
