<template>
  <div class="profile-content">
    <!-- 头像部分 -->
    <div class="avatar-section">
      <div class="avatar-wrapper">
        <el-avatar :src="userInfo?.avatarUrl" :size="120">
          {{ userInfo?.username?.charAt(0) }}
        </el-avatar>
        <div class="avatar-overlay" @click="handleAvatarClick">
          <el-icon :size="24">
            <Camera />
          </el-icon>
          <span>更换头像</span>
        </div>
      </div>
      <input
        ref="avatarInputRef"
        type="file"
        accept="image/*"
        style="display: none"
        @change="handleAvatarChange"
      />
    </div>

    <!-- 用户信息表单 -->
    <div class="form-section">
      <el-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        label-width="100px"
        class="profile-form"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="profileForm.username"
            placeholder="请输入用户名"
            :disabled="!isEditing"
          />
        </el-form-item>

        <el-form-item label="账号">
          <el-input
            v-model="profileForm.account"
            placeholder="账号"
            disabled
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="profileForm.phone"
            placeholder="请输入手机号"
            :disabled="!isEditing"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="profileForm.email"
            placeholder="请输入邮箱"
            :disabled="!isEditing"
          />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input
            v-model="profileForm.address"
            placeholder="请输入地址"
            :disabled="!isEditing"
          />
        </el-form-item>

        <el-form-item label="注册时间">
          <el-input
            :value="formatDate(userInfo?.registerTime)"
            disabled
          />
        </el-form-item>

        <el-form-item label="最后登录">
          <el-input
            :value="formatDate(userInfo?.lastLoginTime)"
            disabled
          />
        </el-form-item>

        <el-form-item>
          <el-button
            v-if="!isEditing"
            type="primary"
            @click="startEdit"
          >
            编辑信息
          </el-button>
          <template v-else>
            <el-button
              type="primary"
              :loading="saving"
              @click="saveProfile"
            >
              保存
            </el-button>
            <el-button @click="cancelEdit">
              取消
            </el-button>
          </template>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Camera } from '@element-plus/icons-vue'
import { getUserInfo, updateUser, uploadAvatar } from '@/api/user'

const profileFormRef = ref()
const avatarInputRef = ref()
const userInfo = ref(null)
const isEditing = ref(false)
const saving = ref(false)
const originalForm = ref({})

// 表单数据
const profileForm = reactive({
  username: '',
  account: '',
  phone: '',
  email: '',
  address: ''
})

// 表单验证规则
const profileRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadUserInfo()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      const user = JSON.parse(storedUserInfo)
      userInfo.value = user
      
      // 从服务器获取最新信息
      const response = await getUserInfo(user.id)
      userInfo.value = response.data
      
      // 更新表单数据
      updateFormData()
      
      // 更新本地存储
      localStorage.setItem('userInfo', JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 更新表单数据
const updateFormData = () => {
  profileForm.username = userInfo.value?.username || ''
  profileForm.account = userInfo.value?.account || ''
  profileForm.phone = userInfo.value?.phone || ''
  profileForm.email = userInfo.value?.email || ''
  profileForm.address = userInfo.value?.address || ''
}

// 开始编辑
const startEdit = () => {
  isEditing.value = true
  originalForm.value = { ...profileForm }
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
  Object.assign(profileForm, originalForm.value)
}

// 保存用户信息
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    saving.value = true
    
    const updateData = {
      username: profileForm.username,
      phone: profileForm.phone,
      email: profileForm.email,
      address: profileForm.address
    }
    
    const response = await updateUser(userInfo.value.id, updateData)
    
    userInfo.value = response.data
    localStorage.setItem('userInfo', JSON.stringify(response.data))
    
    isEditing.value = false
    ElMessage.success('信息更新成功')
    
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 处理头像点击
const handleAvatarClick = () => {
  avatarInputRef.value?.click()
}

// 处理头像更换
const handleAvatarChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 检查文件大小（5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }
  
  try {
    const response = await uploadAvatar(userInfo.value.id, file)
    
    userInfo.value.avatarUrl = response.data
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    
    ElMessage.success('头像更新成功')
    
  } catch (error) {
    console.error('头像上传失败:', error)
  }
  
  // 清空文件输入
  event.target.value = ''
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.profile-content {
  max-width: 600px;
  margin: 0 auto;
}

.avatar-section {
  text-align: center;
  margin-bottom: 40px;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
}

.avatar-wrapper:hover .avatar-overlay {
  opacity: 1;
}

.form-section {
  margin-top: 20px;
}

.profile-form .el-form-item {
  margin-bottom: 24px;
}
</style>
