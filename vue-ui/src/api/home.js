import request from '@/utils/request'

/**
 * 获取首页数据
 */
export function getHomeData(userId) {
  return request({
    url: `/home/<USER>/${userId}`,
    method: 'get'
  })
}

/**
 * 获取好感度排行榜
 */
export function getFavorRanking(userId) {
  return request({
    url: `/home/<USER>/${userId}`,
    method: 'get'
  })
}

/**
 * 获取聊天统计数据
 */
export function getChatStats(userId) {
  return request({
    url: `/home/<USER>/${userId}`,
    method: 'get'
  })
}

/**
 * 获取最近聊天记录
 */
export function getRecentChats(userId, limit = 5) {
  return request({
    url: `/home/<USER>/${userId}`,
    method: 'get',
    params: { limit }
  })
}
