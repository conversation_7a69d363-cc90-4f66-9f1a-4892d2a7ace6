import request from '@/utils/request'

/**
 * 发送消息
 */
export function sendMessage(data) {
  return request({
    url: '/chat/send',
    method: 'post',
    data,
    timeout: 120000 // 聊天请求超时时间设为2分钟
  })
}

/**
 * 获取聊天历史记录
 */
export function getChatHistory(userId, characterId) {
  return request({
    url: `/chat/history/${userId}/${characterId}`,
    method: 'get'
  })
}

/**
 * 获取好感度
 */
export function getFavorValue(userId, characterId) {
  return request({
    url: `/chat/favor/${userId}/${characterId}`,
    method: 'get'
  })
}
