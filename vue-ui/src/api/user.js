import request from '@/utils/request'

/**
 * 用户注册
 * @param {Object} data 注册数据
 */
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}

/**
 * 用户登录
 * @param {Object} data 登录数据
 */
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

/**
 * 获取用户信息
 * @param {Number} userId 用户ID
 */
export function getUserInfo(userId) {
  return request({
    url: `/user/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户信息
 * @param {Number} userId 用户ID
 * @param {Object} data 更新数据
 */
export function updateUser(userId, data) {
  return request({
    url: `/user/${userId}`,
    method: 'put',
    data
  })
}

/**
 * 上传头像
 * @param {Number} userId 用户ID
 * @param {File} file 头像文件
 */
export function uploadAvatar(userId, file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: `/user/${userId}/avatar`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
