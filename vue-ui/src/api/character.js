import request from '@/utils/request'

/**
 * 获取所有角色列表
 */
export function getAllCharacters() {
  return request({
    url: '/character/list',
    method: 'get'
  })
}

/**
 * 根据用户ID获取角色列表（按好感度排序）
 */
export function getCharactersByUserId(userId) {
  return request({
    url: `/character/list/${userId}`,
    method: 'get'
  })
}

/**
 * 根据分类获取角色列表
 */
export function getCharactersByCategory(category) {
  return request({
    url: `/character/category/${category}`,
    method: 'get'
  })
}

/**
 * 获取角色详情
 */
export function getCharacterById(characterId) {
  return request({
    url: `/character/${characterId}`,
    method: 'get'
  })
}

/**
 * 获取所有角色分类
 */
export function getAllCategories() {
  return request({
    url: '/character/categories',
    method: 'get'
  })
}

/**
 * 更新角色信息
 */
export function updateCharacter(characterId, data) {
  return request({
    url: `/character/${characterId}`,
    method: 'put',
    data
  })
}
