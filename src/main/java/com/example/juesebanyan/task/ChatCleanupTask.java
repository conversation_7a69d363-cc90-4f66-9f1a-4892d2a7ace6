package com.example.juesebanyan.task;

import com.example.juesebanyan.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 聊天记录清理定时任务
 */
@Slf4j
@Component
public class ChatCleanupTask {

    @Autowired
    private ChatService chatService;

    /**
     * 每天凌晨2点清理过期聊天记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredChatRecords() {
        log.info("开始清理过期聊天记录...");
        try {
            chatService.cleanExpiredRecords();
            log.info("清理过期聊天记录完成");
        } catch (Exception e) {
            log.error("清理过期聊天记录失败", e);
        }
    }
}
