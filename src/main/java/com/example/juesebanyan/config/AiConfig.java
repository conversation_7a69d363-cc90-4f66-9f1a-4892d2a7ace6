package com.example.juesebanyan.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.openai")
public class AiConfig {
    
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 聊天配置
     */
    private ChatOptions chat = new ChatOptions();
    
    @Data
    public static class ChatOptions {
        private OptionsDetail options = new OptionsDetail();
        
        @Data
        public static class OptionsDetail {
            private String model;
        }
    }
}
