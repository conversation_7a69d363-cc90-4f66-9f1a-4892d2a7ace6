package com.example.juesebanyan.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色实体类
 */
@Data
public class Character {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色名称
     */
    private String name;
    
    /**
     * 角色分类
     */
    private String category;
    
    /**
     * 角色描述
     */
    private String description;
    
    /**
     * 角色人设
     */
    private String personality;
    
    /**
     * 角色头像URL
     */
    private String avatarUrl;
    
    /**
     * 角色背景图URL
     */
    private String backgroundUrl;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
