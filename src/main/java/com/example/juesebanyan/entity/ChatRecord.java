package com.example.juesebanyan.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天记录实体类
 */
@Data
public class ChatRecord {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色ID
     */
    private Long characterId;
    
    /**
     * 消息类型：1-用户消息，2-AI回复，3-AI内心独白
     */
    private Integer messageType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 聊天时间
     */
    private LocalDateTime chatTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
