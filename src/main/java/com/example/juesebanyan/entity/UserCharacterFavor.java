package com.example.juesebanyan.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户角色好感度实体类
 */
@Data
public class UserCharacterFavor {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色ID
     */
    private Long characterId;
    
    /**
     * 好感度值
     */
    private Integer favorValue;
    
    /**
     * 最后聊天时间
     */
    private LocalDateTime lastChatTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
