package com.example.juesebanyan.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long characterId;
    
    /**
     * 用户消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String message;
}
