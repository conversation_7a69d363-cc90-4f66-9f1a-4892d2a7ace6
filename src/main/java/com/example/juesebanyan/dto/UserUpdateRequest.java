package com.example.juesebanyan.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;

/**
 * 用户信息更新请求DTO
 */
@Data
public class UserUpdateRequest {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 地址
     */
    private String address;
}
