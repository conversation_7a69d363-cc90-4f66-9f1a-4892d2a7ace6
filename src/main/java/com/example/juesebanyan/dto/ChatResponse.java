package com.example.juesebanyan.dto;

import lombok.Data;

/**
 * 聊天响应DTO
 */
@Data
public class ChatResponse {
    
    /**
     * AI回复内容
     */
    private String reply;
    
    /**
     * AI内心独白
     */
    private String innerThought;
    
    /**
     * 当前好感度
     */
    private Integer favorValue;
    
    /**
     * 是否是特殊时间触发的对话
     */
    private Boolean isSpecialTime;
    
    public ChatResponse(String reply, String innerThought, Integer favorValue) {
        this.reply = reply;
        this.innerThought = innerThought;
        this.favorValue = favorValue;
        this.isSpecialTime = false;
    }
    
    public ChatResponse(String reply, String innerThought, Integer favorValue, Boolean isSpecialTime) {
        this.reply = reply;
        this.innerThought = innerThought;
        this.favorValue = favorValue;
        this.isSpecialTime = isSpecialTime;
    }
}
