package com.example.juesebanyan.dto;

import com.example.juesebanyan.entity.Character;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 带好感度的角色DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CharacterWithFavor extends Character {
    
    /**
     * 好感度值
     */
    private Integer favorValue;
    
    /**
     * 好感度百分比（用于前端显示进度条）
     */
    private Integer favorPercentage;
    
    public CharacterWithFavor(Character character, Integer favorValue) {
        // 复制父类属性
        this.setId(character.getId());
        this.setName(character.getName());
        this.setCategory(character.getCategory());
        this.setDescription(character.getDescription());
        this.setPersonality(character.getPersonality());
        this.setAvatarUrl(character.getAvatarUrl());
        this.setBackgroundUrl(character.getBackgroundUrl());
        this.setSortOrder(character.getSortOrder());
        this.setIsActive(character.getIsActive());
        this.setCreatedAt(character.getCreatedAt());
        this.setUpdatedAt(character.getUpdatedAt());
        
        // 设置好感度
        this.favorValue = favorValue != null ? favorValue : 0;
        // 计算好感度百分比（假设最大好感度为100）
        this.favorPercentage = Math.min(this.favorValue, 100);
    }
}
