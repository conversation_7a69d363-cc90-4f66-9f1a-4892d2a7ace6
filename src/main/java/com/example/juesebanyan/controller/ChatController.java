package com.example.juesebanyan.controller;

import com.example.juesebanyan.dto.ApiResponse;
import com.example.juesebanyan.dto.ChatRequest;
import com.example.juesebanyan.dto.ChatResponse;
import com.example.juesebanyan.entity.ChatRecord;
import com.example.juesebanyan.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 聊天控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * 发送消息
     */
    @PostMapping("/send")
    public ApiResponse<ChatResponse> sendMessage(@Valid @RequestBody ChatRequest request) {
        try {
            ChatResponse response = chatService.sendMessage(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取聊天历史记录
     */
    @GetMapping("/history/{userId}/{characterId}")
    public ApiResponse<List<ChatRecord>> getChatHistory(@PathVariable Long userId, 
                                                       @PathVariable Long characterId) {
        try {
            List<ChatRecord> history = chatService.getChatHistory(userId, characterId);
            return ApiResponse.success(history);
        } catch (Exception e) {
            log.error("获取聊天历史失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取好感度
     */
    @GetMapping("/favor/{userId}/{characterId}")
    public ApiResponse<Integer> getFavorValue(@PathVariable Long userId, 
                                            @PathVariable Long characterId) {
        try {
            Integer favorValue = chatService.getFavorValue(userId, characterId);
            return ApiResponse.success(favorValue);
        } catch (Exception e) {
            log.error("获取好感度失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
