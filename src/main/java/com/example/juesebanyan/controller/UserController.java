package com.example.juesebanyan.controller;

import com.example.juesebanyan.dto.ApiResponse;
import com.example.juesebanyan.dto.LoginRequest;
import com.example.juesebanyan.dto.RegisterRequest;
import com.example.juesebanyan.dto.UserUpdateRequest;
import com.example.juesebanyan.entity.User;
import com.example.juesebanyan.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<User> register(@Valid @RequestBody RegisterRequest request) {
        try {
            User user = userService.register(request);
            // 不返回密码
            user.setPassword(null);
            return ApiResponse.success("注册成功", user);
        } catch (Exception e) {
            log.error("用户注册失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<User> login(@Valid @RequestBody LoginRequest request) {
        try {
            User user = userService.login(request);
            // 不返回密码
            user.setPassword(null);
            return ApiResponse.success("登录成功", user);
        } catch (Exception e) {
            log.error("用户登录失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{userId}")
    public ApiResponse<User> getUserInfo(@PathVariable Long userId) {
        try {
            User user = userService.getUserById(userId);
            // 不返回密码
            user.setPassword(null);
            return ApiResponse.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    public ApiResponse<User> updateUser(@PathVariable Long userId, 
                                       @Valid @RequestBody UserUpdateRequest request) {
        try {
            User user = userService.updateUser(userId, request);
            // 不返回密码
            user.setPassword(null);
            return ApiResponse.success("更新成功", user);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 上传头像
     */
    @PostMapping("/{userId}/avatar")
    public ApiResponse<String> uploadAvatar(@PathVariable Long userId, 
                                          @RequestParam("file") MultipartFile file) {
        try {
            String avatarUrl = userService.updateAvatar(userId, file);
            return ApiResponse.success("头像上传成功", avatarUrl);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
