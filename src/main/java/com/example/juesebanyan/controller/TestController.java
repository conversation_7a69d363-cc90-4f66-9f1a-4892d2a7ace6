package com.example.juesebanyan.controller;

import com.example.juesebanyan.dto.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    @GetMapping("/hello")
    public ApiResponse<Map<String, String>> hello() {
        Map<String, String> data = new HashMap<>();
        data.put("message", "Hello from backend!");
        data.put("status", "success");
        return ApiResponse.success(data);
    }
}
