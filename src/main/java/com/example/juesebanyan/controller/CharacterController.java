package com.example.juesebanyan.controller;

import com.example.juesebanyan.dto.ApiResponse;
import com.example.juesebanyan.dto.CharacterWithFavor;
import com.example.juesebanyan.entity.Character;
import com.example.juesebanyan.service.CharacterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/character")
public class CharacterController {

    @Autowired
    private CharacterService characterService;

    /**
     * 获取所有角色列表
     */
    @GetMapping("/list")
    public ApiResponse<List<Character>> getAllCharacters() {
        try {
            List<Character> characters = characterService.getAllCharacters();
            return ApiResponse.success(characters);
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据用户ID获取角色列表（按好感度排序）
     */
    @GetMapping("/list/{userId}")
    public ApiResponse<List<CharacterWithFavor>> getCharactersByUserId(@PathVariable Long userId) {
        try {
            List<CharacterWithFavor> characters = characterService.getCharactersByUserId(userId);
            return ApiResponse.success(characters);
        } catch (Exception e) {
            log.error("获取用户角色列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据分类获取角色列表
     */
    @GetMapping("/category/{category}")
    public ApiResponse<List<Character>> getCharactersByCategory(@PathVariable String category) {
        try {
            List<Character> characters = characterService.getCharactersByCategory(category);
            return ApiResponse.success(characters);
        } catch (Exception e) {
            log.error("根据分类获取角色列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{characterId}")
    public ApiResponse<Character> getCharacterById(@PathVariable Long characterId) {
        try {
            Character character = characterService.getCharacterById(characterId);
            return ApiResponse.success(character);
        } catch (Exception e) {
            log.error("获取角色详情失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取所有角色分类
     */
    @GetMapping("/categories")
    public ApiResponse<List<String>> getAllCategories() {
        try {
            List<String> categories = characterService.getAllCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            log.error("获取角色分类失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 更新角色信息
     */
    @PutMapping("/{characterId}")
    public ApiResponse<Character> updateCharacter(@PathVariable Long characterId,
                                                 @RequestBody Character character) {
        try {
            character.setId(characterId);
            Character updatedCharacter = characterService.updateCharacter(character);
            return ApiResponse.success(updatedCharacter);
        } catch (Exception e) {
            log.error("更新角色信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
