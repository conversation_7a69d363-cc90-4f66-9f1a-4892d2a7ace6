package com.example.juesebanyan.controller;

import com.example.juesebanyan.dto.ApiResponse;
import com.example.juesebanyan.service.HomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 首页控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/home")
public class HomeController {

    @Autowired
    private HomeService homeService;

    /**
     * 获取首页数据
     */
    @GetMapping("/data/{userId}")
    public ApiResponse<Map<String, Object>> getHomeData(@PathVariable Long userId) {
        try {
            Map<String, Object> data = homeService.getHomeData(userId);
            return ApiResponse.success(data);
        } catch (Exception e) {
            log.error("获取首页数据失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取好感度排行榜
     */
    @GetMapping("/favor-ranking/{userId}")
    public ApiResponse<Object> getFavorRanking(@PathVariable Long userId) {
        try {
            Object ranking = homeService.getFavorRanking(userId);
            return ApiResponse.success(ranking);
        } catch (Exception e) {
            log.error("获取好感度排行榜失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取聊天统计数据
     */
    @GetMapping("/chat-stats/{userId}")
    public ApiResponse<Object> getChatStats(@PathVariable Long userId) {
        try {
            Object stats = homeService.getChatStats(userId);
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取聊天统计失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取最近聊天记录
     */
    @GetMapping("/recent-chats/{userId}")
    public ApiResponse<Object> getRecentChats(@PathVariable Long userId,
                                            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            Object recentChats = homeService.getRecentChats(userId, limit);
            return ApiResponse.success(recentChats);
        } catch (Exception e) {
            log.error("获取最近聊天记录失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}
