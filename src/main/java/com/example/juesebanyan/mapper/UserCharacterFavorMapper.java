package com.example.juesebanyan.mapper;

import com.example.juesebanyan.entity.UserCharacterFavor;
import org.apache.ibatis.annotations.*;

/**
 * 用户角色好感度Mapper接口
 */
@Mapper
public interface UserCharacterFavorMapper {
    
    /**
     * 查询用户对角色的好感度
     */
    @Select("SELECT * FROM user_character_favor WHERE user_id = #{userId} AND character_id = #{characterId}")
    UserCharacterFavor findByUserAndCharacter(@Param("userId") Long userId, 
                                             @Param("characterId") Long characterId);
    
    /**
     * 插入好感度记录
     */
    @Insert("INSERT INTO user_character_favor (user_id, character_id, favor_value, last_chat_time, created_at, updated_at) " +
            "VALUES (#{userId}, #{characterId}, #{favorValue}, #{lastChatTime}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserCharacterFavor favor);
    
    /**
     * 更新好感度
     */
    @Update("UPDATE user_character_favor SET favor_value = #{favorValue}, last_chat_time = #{lastChatTime}, " +
            "updated_at = #{updatedAt} WHERE user_id = #{userId} AND character_id = #{characterId}")
    int updateFavor(UserCharacterFavor favor);
    
    /**
     * 增加好感度
     */
    @Update("UPDATE user_character_favor SET favor_value = favor_value + #{increment}, " +
            "last_chat_time = #{lastChatTime}, updated_at = #{updatedAt} " +
            "WHERE user_id = #{userId} AND character_id = #{characterId}")
    int incrementFavor(@Param("userId") Long userId, 
                      @Param("characterId") Long characterId, 
                      @Param("increment") Integer increment,
                      @Param("lastChatTime") java.time.LocalDateTime lastChatTime,
                      @Param("updatedAt") java.time.LocalDateTime updatedAt);
}
