package com.example.juesebanyan.mapper;

import com.example.juesebanyan.entity.ChatRecord;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天记录Mapper接口
 */
@Mapper
public interface ChatRecordMapper {

    /**
     * 插入聊天记录
     */
    @Insert("INSERT INTO chat_records (user_id, character_id, message_type, content, chat_time, created_at) " +
            "VALUES (#{userId}, #{characterId}, #{messageType}, #{content}, #{chatTime}, #{createdAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ChatRecord chatRecord);

    /**
     * 查询用户与角色的聊天记录（最近15条）
     */
    @Select("SELECT * FROM chat_records " +
            "WHERE user_id = #{userId} AND character_id = #{characterId} " +
            "ORDER BY chat_time DESC LIMIT 15")
    List<ChatRecord> findRecentByUserAndCharacter(@Param("userId") Long userId,
                                                  @Param("characterId") Long characterId);

    /**
     * 查询用户与角色的所有聊天记录
     */
    @Select("SELECT * FROM chat_records " +
            "WHERE user_id = #{userId} AND character_id = #{characterId} " +
            "ORDER BY chat_time ASC")
    List<ChatRecord> findByUserAndCharacter(@Param("userId") Long userId,
                                           @Param("characterId") Long characterId);

    /**
     * 删除7天前的聊天记录
     */
    @Delete("DELETE FROM chat_records WHERE chat_time < #{sevenDaysAgo}")
    int deleteOldRecords(@Param("sevenDaysAgo") LocalDateTime sevenDaysAgo);

    /**
     * 统计用户与角色的聊天次数
     */
    @Select("SELECT COUNT(*) FROM chat_records " +
            "WHERE user_id = #{userId} AND character_id = #{characterId} AND message_type = 1")
    int countUserMessages(@Param("userId") Long userId, @Param("characterId") Long characterId);

    /**
     * 统计用户总聊天次数
     */
    @Select("SELECT COUNT(*) FROM chat_records WHERE user_id = #{userId} AND message_type = 1")
    int countTotalUserMessages(@Param("userId") Long userId);

    /**
     * 统计用户今日聊天次数
     */
    @Select("SELECT COUNT(*) FROM chat_records " +
            "WHERE user_id = #{userId} AND message_type = 1 AND chat_time >= #{todayStart}")
    int countTodayUserMessages(@Param("userId") Long userId, @Param("todayStart") LocalDateTime todayStart);

    /**
     * 统计用户聊天过的角色数量
     */
    @Select("SELECT COUNT(DISTINCT character_id) FROM chat_records " +
            "WHERE user_id = #{userId} AND message_type = 1")
    int countChatCharacters(@Param("userId") Long userId);

    /**
     * 获取用户最活跃的聊天时段
     */
    @Select("SELECT HOUR(chat_time) as hour, COUNT(*) as count " +
            "FROM chat_records " +
            "WHERE user_id = #{userId} AND message_type = 1 " +
            "GROUP BY HOUR(chat_time) " +
            "ORDER BY count DESC " +
            "LIMIT 1")
    String getMostActiveHour(@Param("userId") Long userId);

    /**
     * 获取最近聊天摘要
     */
    @Select("SELECT cr.*, c.name as character_name, c.avatar_url as character_avatar " +
            "FROM chat_records cr " +
            "LEFT JOIN characters c ON cr.character_id = c.id " +
            "WHERE cr.user_id = #{userId} AND cr.message_type = 1 " +
            "ORDER BY cr.chat_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getRecentChatSummary(@Param("userId") Long userId, @Param("limit") Integer limit);
}
