package com.example.juesebanyan.mapper;

import com.example.juesebanyan.entity.User;
import org.apache.ibatis.annotations.*;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper {

    /**
     * 根据账号查询用户
     */
    @Select("SELECT * FROM users WHERE account = #{account}")
    User findByAccount(@Param("account") String account);

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE id = #{id}")
    User findById(@Param("id") Long id);

    /**
     * 插入用户
     */
    @Insert("INSERT INTO users (username, account, password, phone, email, address, register_time, created_at, updated_at) " +
            "VALUES (#{username}, #{account}, #{password}, #{phone}, #{email}, #{address}, #{registerTime}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);

    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET username = #{username}, phone = #{phone}, email = #{email}, " +
            "address = #{address}, avatar_url = #{avatarUrl}, last_login_time = #{lastLoginTime}, " +
            "updated_at = #{updatedAt} WHERE id = #{id}")
    int update(User user);
}
