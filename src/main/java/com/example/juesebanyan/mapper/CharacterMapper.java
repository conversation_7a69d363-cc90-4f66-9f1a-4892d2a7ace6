package com.example.juesebanyan.mapper;

import com.example.juesebanyan.entity.Character;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 角色Mapper接口
 */
@Mapper
public interface CharacterMapper {

    /**
     * 查询所有启用的角色
     */
    @Select("SELECT * FROM characters WHERE is_active = 1 ORDER BY sort_order")
    List<Character> findAllActive();

    /**
     * 根据分类查询角色
     */
    @Select("SELECT * FROM characters WHERE category = #{category} AND is_active = 1 ORDER BY sort_order")
    List<Character> findByCategory(@Param("category") String category);

    /**
     * 根据ID查询角色
     */
    @Select("SELECT * FROM characters WHERE id = #{id}")
    Character findById(@Param("id") Long id);

    /**
     * 根据用户ID查询角色列表（按好感度排序）
     */
    @Select("SELECT c.* FROM characters c WHERE c.is_active = 1 ORDER BY c.sort_order")
    List<Character> findByUserIdOrderByFavor(@Param("userId") Long userId);

    /**
     * 更新角色信息
     */
    @Update("UPDATE characters SET name = #{name}, category = #{category}, description = #{description}, " +
            "personality = #{personality}, avatar_url = #{avatarUrl}, background_url = #{backgroundUrl}, " +
            "sort_order = #{sortOrder}, is_active = #{isActive}, updated_at = #{updatedAt} " +
            "WHERE id = #{id}")
    int update(Character character);
}
