package com.example.juesebanyan.service;

import com.example.juesebanyan.dto.LoginRequest;
import com.example.juesebanyan.dto.RegisterRequest;
import com.example.juesebanyan.dto.UserUpdateRequest;
import com.example.juesebanyan.entity.User;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 注册成功的用户信息
     */
    User register(RegisterRequest request);
    
    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录成功的用户信息
     */
    User login(LoginRequest request);
    
    /**
     * 根据ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);
    
    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户信息
     */
    User updateUser(Long userId, UserUpdateRequest request);
    
    /**
     * 更新用户头像
     *
     * @param userId 用户ID
     * @param file 头像文件
     * @return 头像URL
     */
    String updateAvatar(Long userId, MultipartFile file);
}
