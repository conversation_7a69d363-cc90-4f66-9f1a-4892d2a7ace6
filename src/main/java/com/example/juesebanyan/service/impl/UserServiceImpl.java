package com.example.juesebanyan.service.impl;

import com.example.juesebanyan.dto.LoginRequest;
import com.example.juesebanyan.dto.RegisterRequest;
import com.example.juesebanyan.dto.UserUpdateRequest;
import com.example.juesebanyan.entity.User;
import com.example.juesebanyan.mapper.UserMapper;
import com.example.juesebanyan.service.OssService;
import com.example.juesebanyan.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private OssService ossService;

    @Override
    public User register(RegisterRequest request) {
        // 检查账号是否已存在
        User existUser = userMapper.findByAccount(request.getAccount());
        if (existUser != null) {
            throw new RuntimeException("账号已存在");
        }

        // 检查用户名是否已存在
        existUser = userMapper.findByUsername(request.getUsername());
        if (existUser != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 创建新用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setRegisterTime(LocalDateTime.now());
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        // 保存用户
        int result = userMapper.insert(user);
        if (result <= 0) {
            throw new RuntimeException("注册失败");
        }

        log.info("用户注册成功，账号: {}", request.getAccount());
        return user;
    }

    @Override
    public User login(LoginRequest request) {
        // 根据账号查询用户
        User user = userMapper.findByAccount(request.getAccount());

        if (user == null) {
            throw new RuntimeException("账号不存在");
        }

        // 验证密码（暂时不加密）
        if (!user.getPassword().equals(request.getPassword())) {
            throw new RuntimeException("密码错误");
        }

        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.update(user);

        log.info("用户登录成功，账号: {}", request.getAccount());
        return user;
    }

    @Override
    public User getUserById(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return user;
    }

    @Override
    public User updateUser(Long userId, UserUpdateRequest request) {
        User user = getUserById(userId);

        // 如果要更新用户名，检查是否已存在
        if (request.getUsername() != null && !request.getUsername().equals(user.getUsername())) {
            User existUser = userMapper.findByUsername(request.getUsername());
            if (existUser != null && !existUser.getId().equals(userId)) {
                throw new RuntimeException("用户名已存在");
            }
        }

        // 更新用户信息
        if (request.getUsername() != null) {
            user.setUsername(request.getUsername());
        }
        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
        }
        if (request.getAddress() != null) {
            user.setAddress(request.getAddress());
        }
        user.setUpdatedAt(LocalDateTime.now());

        int result = userMapper.update(user);
        if (result <= 0) {
            throw new RuntimeException("更新失败");
        }

        log.info("用户信息更新成功，用户ID: {}", userId);
        return user;
    }

    @Override
    public String updateAvatar(Long userId, MultipartFile file) {
        User user = getUserById(userId);

        // 上传头像到OSS
        String avatarUrl = ossService.uploadAvatar(file);

        // 更新用户头像URL
        user.setAvatarUrl(avatarUrl);
        user.setUpdatedAt(LocalDateTime.now());
        userMapper.update(user);

        log.info("用户头像更新成功，用户ID: {}, 头像URL: {}", userId, avatarUrl);
        return avatarUrl;
    }
}
