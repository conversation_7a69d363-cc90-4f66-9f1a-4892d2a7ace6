package com.example.juesebanyan.service.impl;

import com.example.juesebanyan.dto.ChatRequest;
import com.example.juesebanyan.dto.ChatResponse;
import com.example.juesebanyan.entity.Character;
import com.example.juesebanyan.entity.ChatRecord;
import com.example.juesebanyan.entity.UserCharacterFavor;
import com.example.juesebanyan.mapper.ChatRecordMapper;
import com.example.juesebanyan.mapper.UserCharacterFavorMapper;
import com.example.juesebanyan.service.AiService;
import com.example.juesebanyan.service.CharacterService;
import com.example.juesebanyan.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 聊天服务实现类
 */
@Slf4j
@Service
public class ChatServiceImpl implements ChatService {

    @Autowired
    private ChatRecordMapper chatRecordMapper;

    @Autowired
    private UserCharacterFavorMapper favorMapper;

    @Autowired
    private CharacterService characterService;

    @Autowired
    private AiService aiService;

    @Override
    @Transactional
    public ChatResponse sendMessage(ChatRequest request) {
        try {
            // 获取角色信息
            Character character = characterService.getCharacterById(request.getCharacterId());
            
            // 保存用户消息
            ChatRecord userMessage = new ChatRecord();
            userMessage.setUserId(request.getUserId());
            userMessage.setCharacterId(request.getCharacterId());
            userMessage.setMessageType(1); // 用户消息
            userMessage.setContent(request.getMessage());
            userMessage.setChatTime(LocalDateTime.now());
            userMessage.setCreatedAt(LocalDateTime.now());
            chatRecordMapper.insert(userMessage);
            
            // 获取聊天历史（最近15条）
            List<ChatRecord> chatHistory = chatRecordMapper.findRecentByUserAndCharacter(
                request.getUserId(), request.getCharacterId());
            Collections.reverse(chatHistory); // 按时间正序排列
            
            // 生成AI回复
            Map<String, String> aiResponse = aiService.generateResponse(character, request.getMessage(), chatHistory);
            String aiReply = aiResponse.get("reply");
            String innerThought = aiResponse.get("innerThought");
            
            // 保存AI回复
            ChatRecord aiMessage = new ChatRecord();
            aiMessage.setUserId(request.getUserId());
            aiMessage.setCharacterId(request.getCharacterId());
            aiMessage.setMessageType(2); // AI回复
            aiMessage.setContent(aiReply);
            aiMessage.setChatTime(LocalDateTime.now());
            aiMessage.setCreatedAt(LocalDateTime.now());
            chatRecordMapper.insert(aiMessage);
            
            // 保存AI内心独白
            ChatRecord innerThoughtRecord = new ChatRecord();
            innerThoughtRecord.setUserId(request.getUserId());
            innerThoughtRecord.setCharacterId(request.getCharacterId());
            innerThoughtRecord.setMessageType(3); // AI内心独白
            innerThoughtRecord.setContent(innerThought);
            innerThoughtRecord.setChatTime(LocalDateTime.now());
            innerThoughtRecord.setCreatedAt(LocalDateTime.now());
            chatRecordMapper.insert(innerThoughtRecord);
            
            // 更新好感度
            Integer currentFavor = updateFavorValue(request.getUserId(), request.getCharacterId());
            
            return new ChatResponse(aiReply, innerThought, currentFavor);
            
        } catch (Exception e) {
            log.error("发送消息失败", e);
            throw new RuntimeException("发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public List<ChatRecord> getChatHistory(Long userId, Long characterId) {
        return chatRecordMapper.findByUserAndCharacter(userId, characterId);
    }

    @Override
    public void cleanExpiredRecords() {
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        int deletedCount = chatRecordMapper.deleteOldRecords(sevenDaysAgo);
        log.info("清理了 {} 条过期聊天记录", deletedCount);
    }

    @Override
    public Integer getFavorValue(Long userId, Long characterId) {
        UserCharacterFavor favor = favorMapper.findByUserAndCharacter(userId, characterId);
        return favor != null ? favor.getFavorValue() : 0;
    }

    /**
     * 更新好感度
     */
    private Integer updateFavorValue(Long userId, Long characterId) {
        UserCharacterFavor favor = favorMapper.findByUserAndCharacter(userId, characterId);
        LocalDateTime now = LocalDateTime.now();
        
        if (favor == null) {
            // 创建新的好感度记录
            favor = new UserCharacterFavor();
            favor.setUserId(userId);
            favor.setCharacterId(characterId);
            favor.setFavorValue(1); // 初始好感度为1
            favor.setLastChatTime(now);
            favor.setCreatedAt(now);
            favor.setUpdatedAt(now);
            favorMapper.insert(favor);
            return 1;
        } else {
            // 增加好感度
            favorMapper.incrementFavor(userId, characterId, 1, now, now);
            return favor.getFavorValue() + 1;
        }
    }
}
