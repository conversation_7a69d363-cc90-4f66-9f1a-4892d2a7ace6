package com.example.juesebanyan.service.impl;

import com.example.juesebanyan.dto.CharacterWithFavor;
import com.example.juesebanyan.entity.Character;
import com.example.juesebanyan.entity.UserCharacterFavor;
import com.example.juesebanyan.mapper.CharacterMapper;
import com.example.juesebanyan.mapper.UserCharacterFavorMapper;
import com.example.juesebanyan.service.CharacterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
public class CharacterServiceImpl implements CharacterService {

    @Autowired
    private CharacterMapper characterMapper;

    @Autowired
    private UserCharacterFavorMapper favorMapper;

    @Override
    public List<Character> getAllCharacters() {
        return characterMapper.findAllActive();
    }

    @Override
    public List<Character> getCharactersByCategory(String category) {
        return characterMapper.findByCategory(category);
    }

    @Override
    public List<CharacterWithFavor> getCharactersByUserId(Long userId) {
        List<Character> characters = characterMapper.findAllActive();
        List<CharacterWithFavor> result = new ArrayList<>();

        for (Character character : characters) {
            UserCharacterFavor favor = favorMapper.findByUserAndCharacter(userId, character.getId());
            Integer favorValue = favor != null ? favor.getFavorValue() : 0;
            result.add(new CharacterWithFavor(character, favorValue));
        }

        // 按好感度排序
        result.sort((a, b) -> b.getFavorValue().compareTo(a.getFavorValue()));

        return result;
    }

    @Override
    public Character getCharacterById(Long characterId) {
        Character character = characterMapper.findById(characterId);
        if (character == null) {
            throw new RuntimeException("角色不存在");
        }
        return character;
    }

    @Override
    public List<String> getAllCategories() {
        return Arrays.asList(
            "奇幻神话",
            "科幻未来",
            "历史文艺",
            "暗黑悬疑",
            "沙雕无厘头",
            "功能性"
        );
    }

    @Override
    public Character updateCharacter(Character character) {
        // 设置更新时间
        character.setUpdatedAt(java.time.LocalDateTime.now());

        int result = characterMapper.update(character);
        if (result <= 0) {
            throw new RuntimeException("更新角色信息失败");
        }

        log.info("角色信息更新成功，角色ID: {}", character.getId());
        return getCharacterById(character.getId());
    }
}
