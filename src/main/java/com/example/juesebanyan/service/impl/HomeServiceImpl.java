package com.example.juesebanyan.service.impl;

import com.example.juesebanyan.dto.CharacterWithFavor;
import com.example.juesebanyan.entity.ChatRecord;
import com.example.juesebanyan.mapper.ChatRecordMapper;
import com.example.juesebanyan.mapper.UserCharacterFavorMapper;
import com.example.juesebanyan.service.CharacterService;
import com.example.juesebanyan.service.HomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 首页服务实现类
 */
@Slf4j
@Service
public class HomeServiceImpl implements HomeService {

    @Autowired
    private CharacterService characterService;

    @Autowired
    private ChatRecordMapper chatRecordMapper;

    @Autowired
    private UserCharacterFavorMapper favorMapper;

    @Override
    public Map<String, Object> getHomeData(Long userId) {
        Map<String, Object> data = new HashMap<>();

        // 获取好感度前三的角色（轮播图数据）
        List<CharacterWithFavor> topCharacters = getTopFavorCharacters(userId, 3);
        data.put("carouselCharacters", topCharacters);

        // 获取好感度排行榜
        data.put("favorRanking", getFavorRanking(userId));

        // 获取聊天统计
        data.put("chatStats", getChatStats(userId));

        // 获取最近聊天
        data.put("recentChats", getRecentChats(userId, 5));

        // 获取今日推荐角色
        data.put("recommendedCharacters", getRecommendedCharacters(userId));

        return data;
    }

    @Override
    public Object getFavorRanking(Long userId) {
        List<CharacterWithFavor> characters = characterService.getCharactersByUserId(userId);

        // 只返回有好感度的角色，按好感度排序
        return characters.stream()
                .filter(c -> c.getFavorValue() > 0)
                .sorted((a, b) -> b.getFavorValue().compareTo(a.getFavorValue()))
                .limit(10)
                .collect(Collectors.toList());
    }

    @Override
    public Object getChatStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        // 总聊天次数
        int totalChats = chatRecordMapper.countTotalUserMessages(userId);
        stats.put("totalChats", totalChats);

        // 今日聊天次数
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        int todayChats = chatRecordMapper.countTodayUserMessages(userId, todayStart);
        stats.put("todayChats", todayChats);

        // 聊天过的角色数量
        int chatCharacterCount = chatRecordMapper.countChatCharacters(userId);
        stats.put("chatCharacterCount", chatCharacterCount);

        // 最活跃的聊天时段
        String mostActiveHour = chatRecordMapper.getMostActiveHour(userId);
        stats.put("mostActiveHour", mostActiveHour != null ? mostActiveHour : "暂无数据");

        return stats;
    }

    @Override
    public Object getRecentChats(Long userId, Integer limit) {
        return chatRecordMapper.getRecentChatSummary(userId, limit);
    }

    /**
     * 获取好感度前N的角色
     */
    private List<CharacterWithFavor> getTopFavorCharacters(Long userId, int limit) {
        List<CharacterWithFavor> characters = characterService.getCharactersByUserId(userId);

        return characters.stream()
                .sorted((a, b) -> b.getFavorValue().compareTo(a.getFavorValue()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 获取推荐角色
     */
    private List<CharacterWithFavor> getRecommendedCharacters(Long userId) {
        List<CharacterWithFavor> characters = characterService.getCharactersByUserId(userId);

        // 推荐好感度较低或未聊过的角色
        return characters.stream()
                .filter(c -> c.getFavorValue() < 10)
                .sorted((a, b) -> a.getFavorValue().compareTo(b.getFavorValue()))
                .limit(6)
                .collect(Collectors.toList());
    }
}
