package com.example.juesebanyan.service;

import com.example.juesebanyan.dto.CharacterWithFavor;
import com.example.juesebanyan.entity.Character;

import java.util.List;

/**
 * 角色服务接口
 */
public interface CharacterService {

    /**
     * 获取所有角色列表
     */
    List<Character> getAllCharacters();

    /**
     * 根据分类获取角色列表
     */
    List<Character> getCharactersByCategory(String category);

    /**
     * 根据用户ID获取角色列表（按好感度排序）
     */
    List<CharacterWithFavor> getCharactersByUserId(Long userId);

    /**
     * 根据ID获取角色详情
     */
    Character getCharacterById(Long characterId);

    /**
     * 获取所有角色分类
     */
    List<String> getAllCategories();

    /**
     * 更新角色信息
     */
    Character updateCharacter(Character character);
}
