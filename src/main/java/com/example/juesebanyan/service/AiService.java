package com.example.juesebanyan.service;

import com.example.juesebanyan.config.AiConfig;
import com.example.juesebanyan.entity.Character;
import com.example.juesebanyan.entity.ChatRecord;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI服务
 */
@Slf4j
@Service
public class AiService {

    @Autowired
    private AiConfig aiConfig;

    private final RestTemplate restTemplate;

    public AiService() {
        this.restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(java.time.Duration.ofSeconds(30))
                .setReadTimeout(java.time.Duration.ofSeconds(120))
                .build();
    }
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 生成AI回复和内心独白
     */
    public Map<String, String> generateResponse(Character character, String userMessage, List<ChatRecord> chatHistory) {
        try {
            // 构建对话上下文
            List<Map<String, String>> messages = buildMessages(character, userMessage, chatHistory);

            // 调用AI API生成回复
            String aiReply = callAiApi(messages);

            // 生成内心独白
            String innerThought = generateInnerThought(character, userMessage, aiReply);

            // 检查是否是特殊时间
            String specialTimeReply = checkSpecialTime(character);
            if (specialTimeReply != null) {
                aiReply = specialTimeReply;
            }

            Map<String, String> result = new HashMap<>();
            result.put("reply", aiReply);
            result.put("innerThought", innerThought);

            return result;

        } catch (Exception e) {
            log.error("生成AI回复失败", e);

            // 返回默认回复
            Map<String, String> result = new HashMap<>();
            result.put("reply", "抱歉，我现在有点累了，稍后再聊吧~");
            result.put("innerThought", "（系统出现了一些问题...）");
            return result;
        }
    }

    /**
     * 构建对话消息
     */
    private List<Map<String, String>> buildMessages(Character character, String userMessage, List<ChatRecord> chatHistory) {
        List<Map<String, String>> messages = new ArrayList<>();

        // 系统消息（角色人设）
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", character.getPersonality() +
            "\n\n请注意：你需要完全沉浸在这个角色中，用第一人称回应，保持角色的语言风格和性格特点。");
        messages.add(systemMessage);

        // 添加历史对话（只取用户消息和AI回复，不包括内心独白）
        for (ChatRecord record : chatHistory) {
            Map<String, String> message = new HashMap<>();
            if (record.getMessageType() == 1) { // 用户消息
                message.put("role", "user");
                message.put("content", record.getContent());
            } else if (record.getMessageType() == 2) { // AI回复
                message.put("role", "assistant");
                message.put("content", record.getContent());
            }
            if (!message.isEmpty()) {
                messages.add(message);
            }
        }

        // 当前用户消息
        Map<String, String> currentMessage = new HashMap<>();
        currentMessage.put("role", "user");
        currentMessage.put("content", userMessage);
        messages.add(currentMessage);

        return messages;
    }

    /**
     * 调用AI API
     */
    private String callAiApi(List<Map<String, String>> messages) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(aiConfig.getApiKey());

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", aiConfig.getChat().getOptions().getModel());
        requestBody.put("messages", messages);
        requestBody.put("max_tokens", 800);
        requestBody.put("temperature", 0.8);
        requestBody.put("stream", false);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(
            aiConfig.getBaseUrl() + "/chat/completions",
            request,
            String.class
        );

        if (response.getStatusCode() == HttpStatus.OK) {
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            return jsonNode.path("choices").get(0).path("message").path("content").asText();
        } else {
            throw new RuntimeException("AI API调用失败: " + response.getStatusCode());
        }
    }

    /**
     * 生成内心独白
     */
    private String generateInnerThought(Character character, String userMessage, String aiReply) {
        try {
            List<Map<String, String>> messages = new ArrayList<>();

            Map<String, String> systemMessage = new HashMap<>();
            systemMessage.put("role", "system");
            systemMessage.put("content", "你需要为角色生成内心独白。基于角色的性格和刚才的对话，用括号包围的形式生成一段简短的内心想法，体现角色的真实感受或思考过程。内心独白应该与角色的外在回复形成对比或补充。");
            messages.add(systemMessage);

            Map<String, String> userMsg = new HashMap<>();
            userMsg.put("role", "user");
            userMsg.put("content", String.format("角色：%s\n用户说：%s\n角色回复：%s\n请生成这个角色的内心独白：",
                character.getName(), userMessage, aiReply));
            messages.add(userMsg);

            return callAiApi(messages);

        } catch (Exception e) {
            log.error("生成内心独白失败", e);
            return "（内心想法生成中...）";
        }
    }

    /**
     * 检查特殊时间触发
     */
    private String checkSpecialTime(Character character) {
        LocalTime now = LocalTime.now();

        // 午夜12点特殊对话（古宅幽灵）
        if (character.getName().equals("古宅幽灵") &&
            now.getHour() == 0 && now.getMinute() < 5) {
            return "午夜时分...我感受到了更强的力量...让我告诉你一个古老的秘密...";
        }

        return null;
    }
}
