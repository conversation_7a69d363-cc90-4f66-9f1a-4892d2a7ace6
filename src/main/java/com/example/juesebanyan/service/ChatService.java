package com.example.juesebanyan.service;

import com.example.juesebanyan.dto.ChatRequest;
import com.example.juesebanyan.dto.ChatResponse;
import com.example.juesebanyan.entity.ChatRecord;

import java.util.List;

/**
 * 聊天服务接口
 */
public interface ChatService {
    
    /**
     * 发送消息并获取AI回复
     */
    ChatResponse sendMessage(ChatRequest request);
    
    /**
     * 获取聊天历史记录
     */
    List<ChatRecord> getChatHistory(Long userId, Long characterId);
    
    /**
     * 清理过期的聊天记录
     */
    void cleanExpiredRecords();
    
    /**
     * 获取用户与角色的好感度
     */
    Integer getFavorValue(Long userId, Long characterId);
}
