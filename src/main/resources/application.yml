server:
  port: 8080

spring:
  application:
    name: j<PERSON><PERSON><PERSON><PERSON>
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************
    username: root
    password: 200363
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# MyBatis配置
mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 阿里云OSS配置
alioss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  access-key-id: LTAI5t5rQoWPq1Z6YAG6szWE
  access-key-secret: ******************************
  bucket-name: q-ita

# DeepSeek AI配置
ai:
  openai:
    base-url: https://api.deepseek.com
    api-key: ***********************************
    chat:
      options:
        model: deepseek-chat

# 日志配置
logging:
  level:
    com.example.juesebanyan: debug
    org.springframework.web: debug
